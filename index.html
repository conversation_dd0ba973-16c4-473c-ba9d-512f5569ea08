<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <title>Wind Farm Inspection</title>
    <link rel="stylesheet" href="style.css">
    <link rel="manifest" href="manifest.json">
</head>
<body>
    <button class="dark-mode-toggle" id="dark-mode-toggle">🌙 Dark Mode</button>
    
    <!-- Mobile Navigation -->
    <nav class="mobile-nav" id="mobile-nav">
        <button class="nav-btn active" data-section="basic">Basic</button>
        <button class="nav-btn" data-section="time">Time</button>
        <button class="nav-btn" data-section="measurements">Measure</button>
        <button class="nav-btn" data-section="documentation">Docs</button>
    </nav>
    
    <div class="container">
        <header>
            <h1>Wind Farm Inspection Form</h1>
        </header>
        
        <form id="inspection-form">
            <!-- Basic Information Section -->
            <section class="form-section" data-section="basic">
                <h2>Basic Information</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="location">Location Name/Description:</label>
                        <input type="text" id="location" name="location" required autocomplete="address-level2" placeholder="Enter location name or description">
                    </div>
                    <div class="form-group">
                        <label for="gps-coordinates">GPS Coordinates:</label>
                        <input type="text" id="gps-coordinates" name="gps-coordinates" placeholder="Latitude, Longitude" readonly>
                        <button type="button" class="location-btn" id="get-location">Get GPS</button>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="asset-type">Asset Type:</label>
                        <input type="text" id="asset-type" name="asset-type" required>
                    </div>
                    <div class="form-group">
                        <label for="asset-id">Asset ID/Number:</label>
                        <input type="text" id="asset-id" name="asset-id" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="inspector">Inspector:</label>
                        <input type="text" id="inspector" name="inspector" required>
                    </div>
                </div>
            </section>

            <!-- Time & Environmental Section -->
            <section class="form-section" data-section="time">
                <h2>Time & Environmental Conditions</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="start-time">Start Time:</label>
                        <input type="datetime-local" id="start-time" name="start-time" required>
                    </div>
                    <div class="form-group">
                        <label for="finish-time">Finish Time:</label>
                        <input type="datetime-local" id="finish-time" name="finish-time" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="ambient-temp">Ambient Temp (°C):</label>
                        <input type="number" id="ambient-temp" name="ambient-temp" step="0.1" required inputmode="decimal">
                    </div>
                    <div class="form-group">
                        <label for="wind-speed">Wind Speed (m/s):</label>
                        <input type="number" id="wind-speed" name="wind-speed" step="0.1" required inputmode="decimal">
                    </div>
                    <div class="form-group">
                        <label for="humidity">Humidity (%):</label>
                        <input type="number" id="humidity" name="humidity" min="0" max="100" step="0.1" required inputmode="decimal">
                    </div>
                </div>
            </section>

            <!-- Measurements Section -->
            <section class="form-section" data-section="measurements">
                <h2>Measurements</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="power-reading">Power Reading (MW):</label>
                        <input type="number" id="power-reading" name="power-reading" step="0.1" required inputmode="decimal">
                    </div>
                    <div class="form-group">
                        <label for="hotspot">Hotspot Temp (°C):</label>
                        <input type="number" id="hotspot" name="hotspot" step="0.1" required inputmode="decimal">
                    </div>
                    <div class="form-group">
                        <label for="distance">Distance to Target (m):</label>
                        <input type="number" id="distance" name="distance" step="0.1" required inputmode="decimal">
                    </div>
                </div>
            </section>

            <!-- Documentation Section -->
            <section class="form-section" data-section="documentation">
                <h2>Documentation</h2>
                
                <div class="form-group">
                    <label for="camera">Add Picture:</label>
                    <input type="file" id="camera" name="camera" accept="image/*" capture="environment" multiple>
                    <div id="image-preview" class="image-preview"></div>
                </div>

                <div class="form-group">
                    <label for="notes">Notes and Annotations:</label>
                    <textarea id="notes" name="notes" rows="5" placeholder="Enter your observations and notes here..."></textarea>
                </div>

                <div class="form-group">
                    <label>Signature/Annotations:</label>
                    <div class="canvas-container">
                        <canvas id="drawing-canvas" width="300" height="150"></canvas>
                        <div class="canvas-controls">
                            <button type="button" id="clear-canvas">Clear</button>
                        </div>
                    </div>
                </div>
            </section>

            <div class="form-actions">
                <button type="submit">Generate PDF Report</button>
            </div>
        </form>
    </div>

    <!-- Scripts moved outside form -->
    <script src="lib/jspdf.umd.min.js"></script>
    <script src="lib/html2canvas.min.js"></script>
    <script src="script.js"></script>
</body>
</html>