<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline Test - Wind Farm Inspection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>Wind Farm Inspection - Offline Functionality Test</h1>
    
    <div class="test-section">
        <h2>Library Loading Test</h2>
        <button onclick="testLibraries()">Test JavaScript Libraries</button>
        <div id="library-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Local Storage Test</h2>
        <button onclick="testLocalStorage()">Test Local Storage</button>
        <div id="storage-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Canvas Drawing Test</h2>
        <button onclick="testCanvas()">Test Canvas</button>
        <canvas id="test-canvas" width="300" height="150" style="border: 1px solid #ccc; margin: 10px 0;"></canvas>
        <div id="canvas-results"></div>
    </div>
    
    <div class="test-section">
        <h2>PDF Generation Test</h2>
        <button onclick="testPDF()">Test PDF Generation</button>
        <div id="pdf-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Geolocation Test</h2>
        <button onclick="testGeolocation()">Test GPS</button>
        <div id="geo-results"></div>
    </div>
    
    <div class="test-section">
        <h2>File Upload Test</h2>
        <input type="file" id="test-file" accept="image/*" onchange="testFileUpload()">
        <div id="file-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Overall Status</h2>
        <div id="overall-status"></div>
        <button onclick="runAllTests()">Run All Tests</button>
    </div>

    <script src="lib/jspdf.umd.min.js"></script>
    <script src="lib/html2canvas.min.js"></script>
    <script>
        let testResults = {};
        
        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            const className = success ? 'success' : 'error';
            element.innerHTML = `<div class="${className}">${message}</div>`;
            testResults[elementId] = success;
            updateOverallStatus();
        }
        
        function testLibraries() {
            try {
                if (typeof window.jspdf !== 'undefined' && typeof html2canvas !== 'undefined') {
                    showResult('library-results', true, '✓ Both jsPDF and html2canvas libraries loaded successfully');
                } else {
                    showResult('library-results', false, '✗ One or more libraries failed to load');
                }
            } catch (error) {
                showResult('library-results', false, '✗ Error testing libraries: ' + error.message);
            }
        }
        
        function testLocalStorage() {
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                if (value === 'value') {
                    showResult('storage-results', true, '✓ Local storage is working');
                } else {
                    showResult('storage-results', false, '✗ Local storage test failed');
                }
            } catch (error) {
                showResult('storage-results', false, '✗ Local storage error: ' + error.message);
            }
        }
        
        function testCanvas() {
            try {
                const canvas = document.getElementById('test-canvas');
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(10, 10, 100, 50);
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.fillText('Canvas Test', 20, 35);
                showResult('canvas-results', true, '✓ Canvas drawing is working');
            } catch (error) {
                showResult('canvas-results', false, '✗ Canvas error: ' + error.message);
            }
        }
        
        function testPDF() {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                doc.text('Test PDF Generation', 20, 20);
                showResult('pdf-results', true, '✓ PDF generation is working');
            } catch (error) {
                showResult('pdf-results', false, '✗ PDF generation error: ' + error.message);
            }
        }
        
        function testGeolocation() {
            if ('geolocation' in navigator) {
                showResult('geo-results', true, '✓ Geolocation API is available');
            } else {
                showResult('geo-results', false, '✗ Geolocation API is not available');
            }
        }
        
        function testFileUpload() {
            const fileInput = document.getElementById('test-file');
            if (fileInput.files.length > 0) {
                showResult('file-results', true, '✓ File upload is working - Selected: ' + fileInput.files[0].name);
            } else {
                showResult('file-results', false, '✗ No file selected');
            }
        }
        
        function updateOverallStatus() {
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(result => result).length;
            const element = document.getElementById('overall-status');
            
            if (total === 0) return;
            
            const percentage = Math.round((passed / total) * 100);
            const className = percentage === 100 ? 'success' : percentage >= 80 ? 'warning' : 'error';
            element.innerHTML = `<div class="${className}">Tests Passed: ${passed}/${total} (${percentage}%)</div>`;
        }
        
        function runAllTests() {
            testLibraries();
            testLocalStorage();
            testCanvas();
            testPDF();
            testGeolocation();
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
